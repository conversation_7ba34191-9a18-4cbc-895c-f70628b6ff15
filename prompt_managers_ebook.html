<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A4 Ebook Template Generator</title>
    <style>
        /* Control Panel Styling */
        .control-panel {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #2c3e50;
            color: white;
            padding: 20px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .control-panel h1 {
            margin: 0 0 20px 0;
            font-size: 24px;
            color: #ecf0f1;
        }
        
        .upload-section {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .file-upload {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-upload input[type="file"] {
            padding: 8px;
            border: 1px solid #34495e;
            border-radius: 4px;
            background: #ecf0f1;
            color: #2c3e50;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-success:hover {
            background: #229954;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-warning:hover {
            background: #e67e22;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background: #138496;
        }
        
        .status {
            color: #27ae60;
            font-weight: bold;
        }
        
        .error {
            color: #e74c3c;
        }
        
        /* Book Container */
        .book-container {
            margin-top: 180px;
            max-width: 210mm;
            margin-left: auto;
            margin-right: auto;
            background: #f8f9fa;
            padding: 20px;
        }
        
        /* A4 Page Styling */
        @page {
            size: A4;
            margin: 1in;
        }
        
        .page {
            width: 210mm;
            min-height: 297mm;
            padding: 25mm;
            box-sizing: border-box;
            page-break-after: always;
            position: relative;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            font-family: Georgia, serif;
            font-size: 11pt;
            line-height: 1.6;
            color: #333;
        }
        
        .page:last-child {
            page-break-after: auto;
        }
        
        /* Header */
        .header {
            position: absolute;
            top: 15mm;
            left: 25mm;
            right: 25mm;
            font-size: 9pt;
            color: #666;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5pt;
            font-weight: bold;
        }
        
        /* Footer */
        .footer {
            position: absolute;
            bottom: 15mm;
            left: 25mm;
            right: 25mm;
            font-size: 9pt;
            color: #666;
            text-align: center;
            border-top: 1px solid #ddd;
            padding-top: 5pt;
        }
        
        /* Content */
        .content {
            margin-top: 20mm;
            margin-bottom: 15mm;
            min-height: calc(297mm - 80mm);
            max-height: calc(297mm - 80mm);
            overflow: visible; /* Allow content to show, will handle pagination better */
        }
        
        /* Typography */
        h1 {
            font-size: 24pt;
            color: #2c5aa0;
            margin-bottom: 20pt;
            text-align: center;
            page-break-after: avoid;
        }
        
        h2 {
            font-size: 18pt;
            color: #2c5aa0;
            margin-top: 20pt;
            margin-bottom: 12pt;
            page-break-after: avoid;
        }
        
        h3 {
            font-size: 14pt;
            color: #4a7c59;
            margin-top: 16pt;
            margin-bottom: 8pt;
            page-break-after: avoid;
        }
        
        h4 {
            font-size: 12pt;
            color: #666;
            margin-top: 12pt;
            margin-bottom: 6pt;
            page-break-after: avoid;
        }
        
        p {
            margin-bottom: 12pt;
            text-align: justify;
        }
        
        /* Lists */
        ul, ol {
            margin: 12pt 0;
            padding-left: 20pt;
        }
        
        li {
            margin-bottom: 6pt;
        }
        
        /* Tables */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 16pt 0;
            font-size: 10pt;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8pt;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        /* Special Elements */
        .highlight {
            background-color: #fff3cd;
            padding: 8pt;
            border-left: 4px solid #ffc107;
            margin: 12pt 0;
        }
        
        .framework-box {
            border: 2px solid #2c5aa0;
            padding: 12pt;
            margin: 16pt 0;
            background-color: #f8f9ff;
        }
        
        .exercise-box {
            border: 2px solid #4a7c59;
            padding: 12pt;
            margin: 16pt 0;
            background-color: #f8fff8;
        }
        
        /* Table of Contents */
        .toc-entry {
            display: flex;
            justify-content: space-between;
            margin-bottom: 6pt;
            border-bottom: 1px dotted #ccc;
            padding-bottom: 2pt;
        }
        
        .toc-entry .title {
            flex-grow: 1;
        }
        
        .toc-entry .page-num {
            margin-left: 10pt;
            font-weight: bold;
        }
        
        /* Debug mode styles */
        .debug-info {
            background: rgba(231, 76, 60, 0.1);
            border: 1px solid #e74c3c;
            padding: 5pt;
            margin: 5pt 0;
            font-size: 8pt;
            color: #e74c3c;
        }
        
        /* Improved content flow */
        .content h1, .content h2, .content h3, .content h4 {
            page-break-after: avoid;
        }
        
        .content p, .content li {
            orphans: 2;
            widows: 2;
        }
        
        /* Print Styles */
        @media print {
            .control-panel {
                display: none;
            }
            
            .book-container {
                margin-top: 0;
            }
            
            body {
                margin: 0;
            }
            
            .page {
                box-shadow: none;
                margin-bottom: 0;
            }
        }
        @media print {
            .control-panel {
                display: none;
            }
            
            .book-container {
                margin-top: 0;
            }
            
            body {
                margin: 0;
            }
            
            .page {
                box-shadow: none;
                margin-bottom: 0;
            }
        }
        
        /* Hide book initially */
        .book-container.hidden {
            display: none;
        }
        
        /* Template placeholders */
        .template-placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            padding: 20pt;
            text-align: center;
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>

<!-- Control Panel -->
<div class="control-panel">
    <h1>📚 A4 Ebook Template Generator</h1>
    <div class="upload-section">
        <div class="file-upload">
            <label for="contentFile">📁 Upload Content File:</label>
            <input type="file" id="contentFile" accept=".md,.txt,.json" />
        </div>
        
        <div class="file-upload">
            <label for="tocFile">📋 Upload TOC File (optional):</label>
            <input type="file" id="tocFile" accept=".md,.txt,.json" />
        </div>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="processContent()">🔄 Generate Ebook</button>
            <button class="btn btn-success" onclick="downloadTemplate()">💾 Download Template</button>
            <button class="btn btn-warning" onclick="printEbook()">🖨️ Print Ebook</button>
            <button class="btn btn-secondary" onclick="recalculateTOC()">📋 Recalculate TOC</button>
            <button class="btn btn-info" onclick="toggleDebugMode()">🔍 Debug Mode</button>
        </div>
        
        <div id="status" class="status"></div>
    </div>
    
    <div style="margin-top: 15px; font-size: 12px; color: #bdc3c7;">
        <strong>📖 Supported Formats:</strong> Markdown (.md), Text (.txt), JSON (.json) |
        <strong>📏 Output:</strong> Professional A4 format with hierarchy-based content flow |
        <strong>🎯 Features:</strong> Smart pagination based on heading hierarchy, chapters start new pages, sections flow continuously, accurate TOC
    </div>
</div>

<!-- Book Container -->
<div class="book-container hidden" id="bookContainer">
    <!-- Title Page Template -->
    <div class="page">
        <div class="header"></div>
        <div class="content">
            <div style="text-align: center; margin-top: 80pt;">
                <h1 id="bookTitle" style="font-size: 32pt; margin-bottom: 40pt;">📚 BOOK TITLE</h1>
                <h2 id="bookSubtitle" style="font-size: 20pt; color: #666; margin-bottom: 60pt;">Subtitle Here</h2>
                <p id="bookDescription" style="font-size: 14pt; color: #888; margin-bottom: 40pt;">Book description</p>
                <div style="margin-top: 100pt;">
                    <p style="font-size: 12pt;"><strong>Author(s):</strong> <span id="bookAuthor">Author Name</span></p>
                    <p style="font-size: 10pt; color: #666;">Version <span id="bookVersion">1.0</span> • <span id="bookYear">2024</span></p>
                </div>
            </div>
        </div>
        <div class="footer">i</div>
    </div>

    <!-- Table of Contents Template -->
    <div class="page">
        <div class="header">Table of Contents</div>
        <div class="content">
            <h1>📋 Table of Contents</h1>
            <div id="tocContent">
                <div class="template-placeholder">
                    Table of Contents will be auto-generated with accurate page numbers AFTER content distribution is complete
                </div>
            </div>
        </div>
        <div class="footer">ii</div>
    </div>

    <!-- Content Pages Container -->
    <div id="contentPages">
        <div class="page">
            <div class="header">Content</div>
            <div class="content">
                <div class="template-placeholder">
                    <h2>📄 Your Content Will Appear Here</h2>
                    <p>Upload your content file to see it formatted in this professional A4 layout.</p>
                    <h3>✨ Hierarchy-Based Workflow:</h3>
                    <ul>
                        <li>1️⃣ Chapters (H2/##) automatically start new pages</li>
                        <li>2️⃣ Sections (H3+/###) flow continuously when they fit</li>
                        <li>3️⃣ Related sections like "Find Me Online" and "Not Ready Yet" combine</li>
                        <li>4️⃣ Actual page numbers tracked accurately</li>
                        <li>5️⃣ TOC generated with proper hierarchy indentation</li>
                        <li>6️⃣ Professional A4 format with smart page breaks</li>
                    </ul>
                </div>
            </div>
            <div class="footer">1</div>
        </div>
    </div>
</div>

<script>
// Template configuration
let ebookConfig = {
    title: "Your Ebook Title",
    subtitle: "Your Subtitle",
    description: "Your book description",
    author: "Your Name",
    version: "1.0",
    year: new Date().getFullYear(),
    maxContentHeight: 220, // mm - available content height per page
    contentWidth: 160 // mm - available content width
};

// File reading functions
function readFile(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsText(file);
    });
}

// Content processing functions
function parseContent(content, fileType) {
    if (fileType === 'json') {
        try {
            return JSON.parse(content);
        } catch (e) {
            throw new Error('Invalid JSON format');
        }
    }
    
    // Parse markdown or text content
    const sections = [];
    const lines = content.split('\n');
    let currentSection = null;
    
    for (let line of lines) {
        line = line.trim();
        if (!line) continue;
        
        // Detect headings
        if (line.startsWith('#')) {
            if (currentSection) {
                sections.push(currentSection);
            }
            
            const level = (line.match(/^#+/) || [''])[0].length;
            const title = line.replace(/^#+\s*/, '');
            
            currentSection = {
                title: title,
                level: level,
                content: [],
                type: 'chapter'
            };
        } else if (currentSection) {
            currentSection.content.push(line);
        } else {
            // Content before first heading
            if (!sections.length) {
                sections.push({
                    title: 'Introduction',
                    level: 1,
                    content: [],
                    type: 'chapter'
                });
            }
            sections[0].content.push(line);
        }
    }
    
    if (currentSection) {
        sections.push(currentSection);
    }
    
    return sections;
}

function generateTOC(pageTracker) {
    const tocContent = document.getElementById('tocContent');
    tocContent.innerHTML = '';
    
    // Sort entries by page number to ensure correct order
    const sortedEntries = Object.entries(pageTracker).sort((a, b) => a[1].pageNumber - b[1].pageNumber);
    
    sortedEntries.forEach(([title, info]) => {
        const tocEntry = document.createElement('div');
        tocEntry.className = 'toc-entry';
        
        // Updated TOC indentation based on hierarchy:
        // H2 (chapters) = no indent, bold
        // H3 (major sections) = 20pt indent
        // H4+ (subsections) = progressive indent
        const indent = info.level > 2 ? `margin-left: ${(info.level - 2) * 20}pt;` : '';

        tocEntry.innerHTML = `
            <span class="title" style="${indent}">
                ${info.level === 2 ? '<strong>' : ''}
                ${title}
                ${info.level === 2 ? '</strong>' : ''}
            </span>
            <span class="page-num">${info.pageNumber}</span>
        `;
        
        tocContent.appendChild(tocEntry);
    });
}

function formatContent(sections) {
    /*
    HIERARCHY-BASED CONTENT FLOW:

    Based on html_hierarchy_analysis.md structure:
    - H1: Book title only (not used in content sections)
    - H2: Chapter titles - ALWAYS start new pages
    - H3: Major sections - flow continuously when possible
    - H4-H6: Subsections - flow continuously when possible

    FIXED ISSUE: "Find Me Online" and "Not Ready Yet" sections were
    being treated as chapters when they should flow together as sections.

    NEW APPROACH:
    - Only H2 (markdown #) forces new pages (chapters)
    - H3+ (markdown ##, ###, etc.) flow continuously when they fit
    - Related sections combine naturally based on available space
    */
    
    const contentPages = document.getElementById('contentPages');
    contentPages.innerHTML = '';
    
    let pageNumber = 3; // Start after title page and TOC
    let currentChapter = '';
    let currentPageContent = ''; // Accumulate content across sections
    let currentPageHeight = 0; // Track actual height used
    const pageTracker = {}; // Track actual page numbers for TOC
    const maxHeight = ebookConfig.maxContentHeight;
    
    function createPage(content, chapter, pageNum) {
        const pageDiv = document.createElement('div');
        pageDiv.className = 'page';
        pageDiv.innerHTML = `
            <div class="header">${chapter}</div>
            <div class="content">${content}</div>
            <div class="footer">${pageNum}</div>
        `;
        return pageDiv;
    }
    
    function measureContentHeight(element) {
        try {
            // Create temporary element to measure actual height
            const temp = document.createElement('div');
            temp.style.position = 'absolute';
            temp.style.visibility = 'hidden';
            temp.style.width = '160mm'; // Content width
            temp.style.fontFamily = 'Georgia, serif';
            temp.style.fontSize = '11pt';
            temp.style.lineHeight = '1.6';
            temp.style.top = '-9999px';
            temp.innerHTML = element;
            document.body.appendChild(temp);
            const height = temp.offsetHeight * 0.352778; // Convert px to mm (96 DPI)
            document.body.removeChild(temp);
            return Math.max(height, 10); // Minimum 10mm height
        } catch (error) {
            console.warn('Height measurement failed, using fallback:', error);
            // Fallback: estimate based on content length
            const textLength = element.replace(/<[^>]*>/g, '').length;
            return Math.max(textLength * 0.5, 20); // Rough estimate
        }
    }
    
    function addToCurrentPage(htmlContent, sectionTitle, level, forceNewPage = false) {
        const contentHeight = measureContentHeight(htmlContent);

        /*
        UPDATED HIERARCHY-BASED PAGE BREAK LOGIC:
        Based on html_hierarchy_analysis.md:
        - H1: Book title only (not used in content)
        - H2: Chapter titles - ALWAYS start new pages
        - H3: Major sections - can flow continuously
        - H4-H6: Subsections - should flow continuously when possible

        This allows sections like "Find Me Online" and "Not Ready Yet"
        to continue together if they're both H3+ level.
        */

        // Only H2 (level 2) should force new pages - these are chapters
        const isNewChapter = level === 2;

        // Check if content fits on current page
        const wouldFit = (currentPageHeight + contentHeight) <= maxHeight;

        if (debugMode) {
            console.log(`Section: ${sectionTitle}, Level: ${level}, Height: ${contentHeight}mm, Current page: ${currentPageHeight}mm, Would fit: ${wouldFit}, Is chapter: ${isNewChapter}`);
        }

        // Decide whether to continue on current page or start new page
        if (forceNewPage || isNewChapter || (!wouldFit && currentPageContent)) {
            // Finish current page if it has content
            if (currentPageContent) {
                const page = createPage(currentPageContent, currentChapter, pageNumber);
                contentPages.appendChild(page);
                pageNumber++;
            }

            // Start new page
            currentPageContent = htmlContent;
            currentPageHeight = contentHeight;

            // Update chapter name for H2 sections only
            if (isNewChapter) {
                currentChapter = sectionTitle;
            }

            // Record page number for TOC
            if (sectionTitle && !pageTracker[sectionTitle]) {
                pageTracker[sectionTitle] = {
                    pageNumber: pageNumber,
                    level: level
                };
            }
        } else if (wouldFit) {
            // Add to current page - this allows H3+ sections to flow together
            currentPageContent += htmlContent;
            currentPageHeight += contentHeight;

            // Record page number for TOC (use current page)
            if (sectionTitle && !pageTracker[sectionTitle]) {
                pageTracker[sectionTitle] = {
                    pageNumber: pageNumber,
                    level: level
                };
            }
        } else {
            // Content doesn't fit, need to split it
            splitContentAcrossPages(htmlContent, sectionTitle, level);
        }
    }
    
    function splitContentAcrossPages(htmlContent, sectionTitle, level) {
        // Try to split content at natural break points
        const dom = document.createElement('div');
        dom.innerHTML = htmlContent;
        const elements = Array.from(dom.children);
        
        for (let element of elements) {
            const elementHtml = element.outerHTML;
            const elementHeight = measureContentHeight(elementHtml);
            
            // Check if element fits on current page
            if ((currentPageHeight + elementHeight) <= maxHeight && currentPageContent) {
                // Add to current page
                currentPageContent += elementHtml;
                currentPageHeight += elementHeight;
            } else {
                // Need new page for this element
                if (currentPageContent) {
                    // Finish current page
                    const page = createPage(currentPageContent, currentChapter, pageNumber);
                    contentPages.appendChild(page);
                    pageNumber++;
                }
                
                // Start new page with this element
                currentPageContent = elementHtml;
                currentPageHeight = elementHeight;
                
                // Record page number for TOC if first element of section
                if (sectionTitle && !pageTracker[sectionTitle] && element === elements[0]) {
                    pageTracker[sectionTitle] = {
                        pageNumber: pageNumber,
                        level: level
                    };
                }
            }
        }
    }
    
    // Process all sections with continuous flow
    sections.forEach(section => {
        // Prepare section content
        let sectionHtml = '';
        
        // Add section title with proper hierarchy mapping
        // Based on html_hierarchy_analysis.md:
        // Markdown # (level 1) -> HTML H2 (chapters)
        // Markdown ## (level 2) -> HTML H3 (major sections)
        // Markdown ### (level 3) -> HTML H4 (subsections)
        // etc.
        const htmlLevel = Math.min(section.level + 1, 6);
        const headingTag = `h${htmlLevel}`;
        sectionHtml += `<${headingTag}>${section.title}</${headingTag}>`;
        
        // Add section content
        section.content.forEach(paragraph => {
            if (paragraph.trim()) {
                // Convert markdown-style formatting
                let formattedParagraph = paragraph
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/`(.*?)`/g, '<code>$1</code>');
                
                // Handle special formatting blocks
                if (paragraph.includes('💡') || paragraph.includes('⚠️') || paragraph.includes('🔑')) {
                    sectionHtml += `<div class="highlight"><p>${formattedParagraph}</p></div>`;
                } else if (paragraph.includes('🏋️') || paragraph.includes('Exercise')) {
                    sectionHtml += `<div class="exercise-box"><p>${formattedParagraph}</p></div>`;
                } else if (paragraph.includes('📊') || paragraph.includes('Framework')) {
                    sectionHtml += `<div class="framework-box"><p>${formattedParagraph}</p></div>`;
                } else if (paragraph.startsWith('- ') || paragraph.startsWith('* ')) {
                    sectionHtml += `<ul><li>${formattedParagraph.substring(2)}</li></ul>`;
                } else if (paragraph.match(/^\d+\. /)) {
                    sectionHtml += `<ol><li>${formattedParagraph.replace(/^\d+\. /, '')}</li></ol>`;
                } else {
                    sectionHtml += `<p>${formattedParagraph}</p>`;
                }
            }
        });
        
        // Add section to pages with hierarchy-aware continuous flow logic
        // Use the htmlLevel already calculated above for proper page break decisions
        addToCurrentPage(sectionHtml, section.title, htmlLevel);
    });
    
    // Create final page with remaining content
    if (currentPageContent) {
        const page = createPage(currentPageContent, currentChapter, pageNumber);
        contentPages.appendChild(page);
    }
    
    // Return page tracker for TOC generation
    return pageTracker;
}

// Main processing function
async function processContent() {
    const contentFile = document.getElementById('contentFile').files[0];
    const status = document.getElementById('status');
    
    if (!contentFile) {
        status.innerHTML = '<span class="error">❌ Please select a content file</span>';
        return;
    }
    
    try {
        status.innerHTML = '⏳ Processing content...';
        
        // Read and parse content
        const content = await readFile(contentFile);
        const fileExtension = contentFile.name.split('.').pop().toLowerCase();
        const sections = parseContent(content, fileExtension);
        
        // Extract metadata if provided
        if (fileExtension === 'json' && sections.metadata) {
            Object.assign(ebookConfig, sections.metadata);
            sections = sections.content || sections;
        }
        
        // Update title page
        document.getElementById('bookTitle').textContent = ebookConfig.title;
        document.getElementById('bookSubtitle').textContent = ebookConfig.subtitle;
        document.getElementById('bookDescription').textContent = ebookConfig.description;
        document.getElementById('bookAuthor').textContent = ebookConfig.author;
        document.getElementById('bookVersion').textContent = ebookConfig.version;
        document.getElementById('bookYear').textContent = ebookConfig.year;
        
        // First: Format content with continuous flow
        status.innerHTML = '📄 Distributing content with continuous flow - combining sections when they fit together...';
        const pageTracker = formatContent(sections);
        
        // Second: Generate TOC with actual page numbers
        status.innerHTML = '📋 Generating Table of Contents with accurate page references...';
        generateTOC(pageTracker);
        
        // Show the book
        document.getElementById('bookContainer').classList.remove('hidden');
        
        // Calculate total pages
        const totalPages = Object.keys(pageTracker).length > 0 ? 
            Math.max(...Object.values(pageTracker).map(info => info.pageNumber)) + 1 : 3;
        
        status.innerHTML = `✅ Ebook generated with hierarchy-based flow! ${sections.length} sections distributed across ${totalPages} pages. Chapters (H2) start new pages, while sections (H3+) flow continuously when they fit together.`;
        
        // Scroll to book
        document.getElementById('bookContainer').scrollIntoView({ behavior: 'smooth' });
        
    } catch (error) {
        status.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
    }
}

// Add function to recalculate TOC if needed
function recalculateTOC() {
    const status = document.getElementById('status');
    status.innerHTML = '📋 Recalculating Table of Contents...';
    
    const contentPages = document.getElementById('contentPages');
    const pages = contentPages.querySelectorAll('.page');
    const pageTracker = {};
    
    pages.forEach((page, index) => {
        const pageNumber = index + 3; // Account for title page and TOC
        const headers = page.querySelectorAll('h1, h2, h3, h4, h5, h6');
        
        headers.forEach(header => {
            const level = parseInt(header.tagName.substring(1));
            const title = header.textContent.trim();
            
            if (!pageTracker[title]) {
                pageTracker[title] = {
                    pageNumber: pageNumber,
                    level: level
                };
            }
        });
    });
    
    generateTOC(pageTracker);
    
    const totalPages = Object.keys(pageTracker).length > 0 ? 
        Math.max(...Object.values(pageTracker).map(info => info.pageNumber)) + 1 : 3;
    
    status.innerHTML = `✅ TOC recalculated successfully! ${Object.keys(pageTracker).length} sections found across ${totalPages} pages.`;
    
    return pageTracker;
}

// Utility functions
function downloadTemplate() {
    const template = '# Your Ebook Title\n\n' +
        '## Chapter 1: Introduction\n\n' +
        'Welcome to your first chapter. This is how you format content for the ebook generator.\n\n' +
        '### Key Features\n\n' +
        '- Automatic page breaks\n' +
        '- Professional formatting\n' +
        '- Table of contents generation\n' +
        '- Headers and footers\n\n' +
        '### Formatting Guide\n\n' +
        '**Bold text** uses double asterisks\n' +
        '*Italic text* uses single asterisks\n' +
        '`Code text` uses backticks\n\n' +
        '#### Lists\n\n' +
        '1. Numbered lists work automatically\n' +
        '2. Just use the standard format\n' +
        '3. They\'ll be styled properly\n\n' +
        '- Bullet points also work\n' +
        '- Use dashes or asterisks\n' +
        '- Both will be converted\n\n' +
        '## Chapter 2: Advanced Features\n\n' +
        'You can include tables, images, and other markdown elements.\n\n' +
        '### JSON Format Alternative\n\n' +
        'Alternatively, you can upload a JSON file with this structure:\n\n' +
        '{\n' +
        '  "metadata": {\n' +
        '    "title": "Your Book Title",\n' +
        '    "subtitle": "Your Subtitle",\n' +
        '    "author": "Your Name",\n' +
        '    "description": "Book description",\n' +
        '    "version": "1.0"\n' +
        '  },\n' +
        '  "content": [\n' +
        '    {\n' +
        '      "title": "Chapter 1",\n' +
        '      "level": 1,\n' +
        '      "content": ["Paragraph 1", "Paragraph 2"]\n' +
        '    }\n' +
        '  ]\n' +
        '}';
    
    const blob = new Blob([template], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'ebook-template.md';
    a.click();
    URL.revokeObjectURL(url);
}

// Add debug mode functionality
let debugMode = false;

function toggleDebugMode() {
    debugMode = !debugMode;
    const pages = document.querySelectorAll('.page');
    const status = document.getElementById('status');
    
    if (debugMode) {
        pages.forEach((page, index) => {
            const content = page.querySelector('.content');
            content.style.border = '2px dashed #e74c3c';
            content.style.position = 'relative';
            
            // Add height indicator
            const heightIndicator = document.createElement('div');
            heightIndicator.style.position = 'absolute';
            heightIndicator.style.top = '0';
            heightIndicator.style.right = '0';
            heightIndicator.style.background = '#e74c3c';
            heightIndicator.style.color = 'white';
            heightIndicator.style.padding = '2px 5px';
            heightIndicator.style.fontSize = '8pt';
            heightIndicator.textContent = `H: ${content.offsetHeight}px`;
            content.appendChild(heightIndicator);
        });
        status.innerHTML = '🔍 Debug mode enabled - red borders show content areas with height indicators';
    } else {
        pages.forEach(page => {
            const content = page.querySelector('.content');
            content.style.border = 'none';
            const heightIndicator = content.querySelector('div[style*="position: absolute"]');
            if (heightIndicator) {
                heightIndicator.remove();
            }
        });
        status.innerHTML = '✅ Debug mode disabled';
    }
}

function printEbook() {
    // Temporarily disable debug mode for printing
    const wasDebugMode = debugMode;
    if (debugMode) {
        toggleDebugMode();
    }
    
    window.print();
    
    // Restore debug mode after printing
    if (wasDebugMode) {
        setTimeout(() => toggleDebugMode(), 1000);
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('status').innerHTML = '📚 Ready to generate your ebook with hierarchy-based content flow. Chapters (H2) start new pages, sections (H3+) flow continuously when they fit together.';
});
</script>

</body>
</html>