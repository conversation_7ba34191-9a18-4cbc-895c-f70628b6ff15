<!DOCTYPE html>
<html>
<head>
    <title>Debug processContent</title>
</head>
<body>
    <button onclick="processContent()">Test processContent</button>
    <input type="file" id="contentFile" />
    <div id="status"></div>

    <script>
    // Test minimal version of processContent
    async function processContent() {
        console.log('processContent called');
        const contentFile = document.getElementById('contentFile').files[0];
        const status = document.getElementById('status');
        
        if (!contentFile) {
            status.innerHTML = '<span style="color: red;">❌ Please select a content file</span>';
            return;
        }
        
        status.innerHTML = '✅ processContent is working!';
    }

    // Check if function is defined on load
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded');
        console.log('processContent type:', typeof processContent);
        console.log('processContent function:', processContent);
        
        // Test if we can call it programmatically
        try {
            // Don't actually call it, just test if it exists
            if (typeof processContent === 'function') {
                document.getElementById('status').innerHTML = '✅ processContent is defined and ready';
            } else {
                document.getElementById('status').innerHTML = '❌ processContent is not a function';
            }
        } catch (e) {
            console.error('Error testing processContent:', e);
            document.getElementById('status').innerHTML = '❌ Error: ' + e.message;
        }
    });
    </script>
</body>
</html>
